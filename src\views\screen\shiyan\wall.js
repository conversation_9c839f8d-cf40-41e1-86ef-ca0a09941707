import * as THREE from 'three';
// import { GUI } from 'three/examples/jsm/libs/lil-gui.module.min.js';
import scene from './motor';

export function initWall(RmRoomList){
    // 检查RmRoomList是否有效
    if (!RmRoomList || !Array.isArray(RmRoomList) || RmRoomList.length === 0) {
        console.warn('RmRoomList为空或无效，使用默认墙壁尺寸');
        // 使用默认尺寸
        const width = 15; // 默认长度
        const length = 8; // 默认宽度
        const height = 4; // 默认高度
        return createWallGroup(width, length, height, '默认机房');
    }

    const roomData = RmRoomList[0];
    const width = Number(roomData.length) || 15; // 14.916m，如果没有数据则使用默认值
    const length = Number(roomData.width) || 8; // 8.3m
    const height = Number(roomData.height) || 4; // 4m
    const roomName = roomData.roomName || '机房';

    return createWallGroup(width, length, height, roomName);
}

function createWallGroup(width, length, height, roomName) {
    const group = new THREE.Group();
    group.name = roomName;
    // 创建墙壁几何体
    const qiangGeometry1 = new THREE.BoxGeometry(length + 0.1, height, 0.1);

    // 创建墙壁材质
    const sideMaterial = new THREE.MeshBasicMaterial({
        color: 0xcccccc, // 基础颜色
        side: THREE.DoubleSide, // 双面渲染
    });
    const topMaterial = new THREE.MeshBasicMaterial({
        color: 0xffffff,
        side: THREE.DoubleSide,
    });

    const materials = [
        sideMaterial, // 右
        sideMaterial, // 左
        topMaterial,  // 顶
        sideMaterial, // 底
        sideMaterial, // 前
        sideMaterial  // 后
    ];

    // 创建四面墙壁
    const qiang = new THREE.Mesh(qiangGeometry1, materials);
    qiang.castShadow = true;
    qiang.receiveShadow = true;
    qiang.translateX(length / 2);
    qiang.translateY(height / 2);

    const qiang2 = qiang.clone();
    qiang2.translateZ(width);

    const qiangGeometry2 = new THREE.BoxGeometry(width, height, 0.1);
    const qiang3 = new THREE.Mesh(qiangGeometry2, materials);
    qiang3.rotateY(Math.PI / 2);
    qiang3.translateZ(length);
    qiang3.translateX(-width / 2);
    qiang3.translateY(height / 2);

    const qiang4 = qiang3.clone();
    qiang4.translateZ(-length);

    group.add(qiang, qiang2, qiang3, qiang4);
    

    // 创建地板
    const floorGeometry = new THREE.PlaneGeometry(length, width);
    const floortexture = new THREE.TextureLoader().load(require('./pic/cizhuan.jpg'));
    const floorMaterial = new THREE.MeshBasicMaterial({
        map: floortexture,
        side: THREE.DoubleSide,
        color: 0xffffff,
    });

    const floor = new THREE.Mesh(floorGeometry, floorMaterial);
    floor.translateX(length / 2);
    floor.translateZ(width / 2);

    floortexture.wrapS = THREE.RepeatWrapping;
    floortexture.wrapT = THREE.RepeatWrapping;
    floortexture.repeat.set(100, 100);
    floor.rotateX(Math.PI / 2);

    group.add(floor);
    return group;
}
